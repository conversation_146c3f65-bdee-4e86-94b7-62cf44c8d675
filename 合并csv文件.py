#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并data_output/csv文件夹中的所有CSV文件，并按路段ID和采集时间排序
"""

import pandas as pd
import os
import glob
from datetime import datetime

def merge_csv_files():
    """
    合并所有CSV文件并按路段ID和采集时间排序
    """
    # CSV文件路径
    csv_folder = "data_output/csv"
    output_file = "data_output/merged_traffic_data.csv"
    
    # 检查文件夹是否存在
    if not os.path.exists(csv_folder):
        print(f"错误：文件夹 {csv_folder} 不存在")
        return
    
    # 获取所有CSV文件
    csv_files = glob.glob(os.path.join(csv_folder, "*.csv"))
    
    if not csv_files:
        print(f"错误：在 {csv_folder} 中没有找到CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    
    # 存储所有数据的列表
    all_data = []
    
    # 读取每个CSV文件
    for i, file_path in enumerate(csv_files, 1):
        try:
            print(f"正在处理文件 {i}/{len(csv_files)}: {os.path.basename(file_path)}")
            
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 检查是否有数据
            if not df.empty:
                all_data.append(df)
            else:
                print(f"警告：文件 {file_path} 为空")
                
        except Exception as e:
            print(f"错误：读取文件 {file_path} 时出错: {e}")
            continue
    
    if not all_data:
        print("错误：没有成功读取任何数据")
        return
    
    # 合并所有数据
    print("正在合并数据...")
    merged_df = pd.concat(all_data, ignore_index=True)
    
    print(f"合并前总行数: {sum(len(df) for df in all_data)}")
    print(f"合并后总行数: {len(merged_df)}")
    
    # 转换采集时间为datetime类型以便排序
    print("正在转换时间格式...")
    merged_df['采集时间'] = pd.to_datetime(merged_df['采集时间'])
    
    # 按路段ID和采集时间排序
    print("正在排序...")
    merged_df = merged_df.sort_values(['路段ID', '采集时间'])
    
    # 重置索引
    merged_df = merged_df.reset_index(drop=True)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 保存合并后的文件
    print(f"正在保存到 {output_file}...")
    merged_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"合并完成！")
    print(f"输出文件: {output_file}")
    print(f"总行数: {len(merged_df)}")
    print(f"路段数量: {merged_df['路段ID'].nunique()}")
    print(f"时间范围: {merged_df['采集时间'].min()} 到 {merged_df['采集时间'].max()}")
    
    # 显示前几行数据作为预览
    print("\n前10行数据预览:")
    print(merged_df.head(10).to_string(index=False))
    
    return output_file

if __name__ == "__main__":
    merge_csv_files()
